FROM apache/airflow:2.10.5

USER root
COPY /docker/entrypoint.sh /entrypoint.sh

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

USER airflow

# RUN chmod +x entrypoint.sh

# Copy requirements and entrypoint
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

ENTRYPOINT ["/entrypoint.sh"]
