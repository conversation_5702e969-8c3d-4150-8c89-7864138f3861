from airflow import D<PERSON>
from datetime import timedelta
from airflow.utils.dates import days_ago
from airflow.operators.python import PythonOperator
from scripts.product_data.fetch_products import fetch_magento_products, fetch_product_orders
from scripts.product_data.fetch_stock_info import stock_info_data_fetch
from scripts.product_data.prepare_product_data import preprocess_product_data

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=5),
}

dag = DAG(
    "product_pipeline",
    default_args=default_args,
    description="Pipeline for processing product data",
    schedule_interval="@daily",  # Adjust as needed
    start_date=days_ago(1),
    catchup=False,
    tags=["product"],
    params={"skus": []},  # User can override this when triggering the DAG manually
)

# Task to fetch inventory data
fetch_product = PythonOperator(
    task_id="fetch_products",
    python_callable=fetch_magento_products,
    dag=dag,
)

# Task to fetch product orders
fetch_product_orders_task = PythonOperator(
    task_id="fetch_product_orders",
    python_callable=fetch_product_orders,
    dag=dag,
)

# Task to preprocess inventory data
preprocess_product_task = PythonOperator(
    task_id="prepare_product_data",
    python_callable=preprocess_product_data,
    dag=dag,
)

# Task to fetch stock information
fetch_stock_info_task = PythonOperator(
    task_id="fetch_stock_info",
    python_callable=stock_info_data_fetch,
    dag=dag,
)

# Define task dependencies
fetch_product >> fetch_product_orders_task >> preprocess_product_task
fetch_product >> fetch_stock_info_task
