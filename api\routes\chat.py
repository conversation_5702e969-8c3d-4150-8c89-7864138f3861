import json
from openai import <PERSON>A<PERSON>
from typing import Any, Dict
from datetime import datetime, timed<PERSON>ta
from mongoengine.errors import DoesNotExist
from fastapi.responses import StreamingResponse
from agentic_ai.utils.llm_utils import LLMConfig
from database.chat import ChatSession, ChatMessage
from agentic_ai.orchestrator.graph import AgentOrchestrator
from schemas.message_schema import ChatResponse, ChatRequest
from fastapi import APIRouter, BackgroundTasks, HTTPException


chat_router = APIRouter()
llm_config = LLMConfig()
orchestrator = AgentOrchestrator(llm_config)
client = OpenAI()


@chat_router.post("/query", response_model=ChatResponse)
async def chat_query(request: ChatRequest, background_tasks: BackgroundTasks):
    try:
        session_id = request.session_id

        # 1. Fetch session
        session = ChatSession.objects(session_id=session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")

        # 2. Check if this is the first user message
        #    (assumes you log each message in a Message collection)
        message_count = ChatMessage.objects(session_id=session_id).count()
        if message_count == 0:
            try:
                print("Generating session title...")
                prompt = (
                    "Summarize this user query as a short session title:\n\n"
                    f"{request.query}"
                )
                ai_response = client.chat.completions.create(
                    model=request.model_name,
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an assistant that generates short, clear session titles.",
                        },
                        {"role": "user", "content": prompt},
                    ],
                    max_tokens=20,
                )

                session_title = ai_response.choices[0].message.content.strip()

                # Save the session title to the database
                session.name = session_title
                session.save()  # Use save() instead of update()

                print(f"Session title generated and saved: {session_title}")
            except Exception as e:
                print("Failed to generate or save session title:", e)
        else:
            session_title = (
                session.name
            )  # Use the existing session name for subsequent queries

        result = await orchestrator.process_query(
            query=request.query,
            model_name=request.model_name,
            session_id=session_id,
            language=request.language,
        )

        # Always include the session name in the metadata

        response = ChatResponse(
            response=result["response"],
            session_id=session_id,
            timestamp=datetime.now(),
            name=session_title,
        )

        # Store message in database asynchronously
        background_tasks.add_task(
            store_message,
            session_id=session_id,
            message=request.query,
            response=result["response"],
        )

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@chat_router.post("/stream")
async def stream_query(request: ChatRequest):
    session_id = request.session_id

    async def event_generator():
        try:
            async for chunk in orchestrator.process_stream(
                query=request.query,
                model_name=request.model_name,
                session_id=session_id,
            ):
                # Yield each chunk as a properly formatted Server-Sent Event
                yield f"data: {json.dumps(chunk)}\n\n"
        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"

        yield {"final": True, "message": "This is the final part of the stream"}

    return StreamingResponse(event_generator(), media_type="text/event-stream")


def store_message(session_id: str, message: str, response: str):
    """Store chat message using MongoEngine."""
    try:
        session = ChatSession.objects.get(session_id=session_id)

        # Create a new ChatMessage
        chat_msg = ChatMessage(
            session_id=session_id,
            message=message,
            response=response,
            timestamp=datetime.now(),
        )
        chat_msg.save()

        # Append to session and update last activity
        session.update(push__messages=chat_msg, set__last_activity=datetime.now())

    except DoesNotExist:
        print(f"Session with ID {session_id} not found. Cannot store message.")
    except Exception as e:
        print(f"Error storing message: {str(e)}")
