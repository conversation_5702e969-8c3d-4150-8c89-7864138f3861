import os,re
import json
import logging
from typing import Any, Dict
from qdrant_client import Qdrant<PERSON>lient
from ..state.agent_state import AgentState
from langchain_qdrant import QdrantVectorStore
from langchain.schema import HumanMessage, SystemMessage
from ..utils.llm_utils import LLMConfig, create_system_prompt
from qdrant_client.http.models import Filter, FieldCondition, MatchValue


QDRANT_HOST = os.environ.get("QDRANT_HOST", "")
QDRANT_PORT = os.environ.get("QDRANT_PORT", "")


class ProductAgent:
    """Agent specialized in handling product-related queries."""

    def __init__(self, llm_config: LLMConfig):
        self.llm = llm_config.get_llm()
        self.system_prompt = create_system_prompt("product")
        self.qdrant = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
        self.model = llm_config.get_embedding_model()
        self.collection_name = "product_data"
        self.vector_store = QdrantVectorStore(
            client=self.qdrant,
            collection_name=self.collection_name,
            embedding=self.model,
        )
        self.metadata_extractor = ProductMetadataFilterExtractor(llm_config)

    async def process_query(self, query: str, state: AgentState) -> Dict[str, Any]:
        """Process a product-related query."""

        # Check if this agent depends on other agents
        dependencies = state.get("agent_dependencies", {}).get("product_agent", [])
        dependency_context = ""

        # If we have dependencies, extract relevant information from their outputs
        if dependencies:
            dependency_context = "Information from dependent agents:\n\n"
            for dep in dependencies:
                if dep in state["agent_outputs"]:
                    dependency_context += f"--- {dep.upper()} OUTPUT ---\n"
                    dependency_context += state["agent_outputs"][dep] + "\n\n"

        # Combine the original query with dependency context
        enhanced_query = query
        if dependency_context:
            enhanced_query = (
                f"{query}\n\nContext from other agents:\n{dependency_context}"
            )

        language = state.get("language", "en-US")
        search_results = []

        print(f"[INFO] ProductAgent: Processing query {enhanced_query}")
        try:
            # Extract metadata filters
            metadata_filter = self.metadata_extractor.extract_product_metadata_filters(
                enhanced_query
            )
            print(f"[DEBUG] Metadata filter: {metadata_filter}")

            search_results = self.search_with_escalating_filters(
                enhanced_query, metadata_filter, self.vector_store, self._embed_query
            )

            print(
                f"[DEBUG] Found {len(search_results)} search results \n{search_results}\n"
            )

        except Exception as e:
            print("[ERROR] Vector Search Failed")
            print(e)

        context = self._prepare_context(search_results)

        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"Context: {context}\n\nQuery: {enhanced_query}"),
        ]

        print(messages)
        response = self.llm.invoke(messages)
        print("[LLM] Product Agent Response ---> \n", response.content)

        return {
            "response": response.content,
        }

    def _embed_query(self, query: str) -> list[float]:
        """Convert query to vector embedding."""
        return self.model.embed_query(query)

    def _prepare_context(self, search_results: list) -> str:
        """Prepare context from vector search results and replace image paths with full staging URLs."""
        print("----" * 30)
        print(search_results)
        print("----" * 30)

        context = "\n".join([result.page_content for result in search_results])
        staging_url = os.getenv("MAGENTO_API_URL")

        # Replace relative paths like /p/a/img.jpg with full URL
        def replacer(match):
            path = match.group(1)
            return f'{staging_url}media/catalog/product{path}'
        
        context = re.sub(r'(?<!http)(/[\w/-]+\.(?:jpg|jpeg|png|webp))', replacer, context, flags=re.IGNORECASE)

        # Deduplicate Markdown and HTML image tags by URL
        seen_urls = set()

        # Remove duplicate Markdown images
        def dedup_markdown_images(match):
            alt = match.group(1)
            url = match.group(2)
            if url in seen_urls:
                return ''
            seen_urls.add(url)
            return f'![{alt}]({url})'
        
        context = re.sub(r'!\[([^\]]*)\]\((https?://[^\)]+)\)', dedup_markdown_images, context)

        # Remove duplicate HTML <img> tags
        def dedup_html_images(match):
            url = match.group(1)
            if url in seen_urls:
                return ''
            seen_urls.add(url)
            return f'<img src="{url}" style="width:189px; height:189px; object-fit:contain;" />'

        context = re.sub(r'<img\s+[^>]*src="([^"]+)"[^>]*>', dedup_html_images, context)

        return context
    
    async def run(self, state: AgentState) -> AgentState:
        print("-----" * 20)
        print(f"[START] Product Agent")
        query = state["agent_queries"].get("product_agent", state["query"])

        results = {}
        if isinstance(query, list):
            results = {}
            for q in query:
                result = await self.process_query(q, state)
                results[q] = result["response"]
        else:
            result = await self.process_query(query, state)
            results[query] = result["response"]

        state["agent_outputs"]["product_agent"] = json.dumps(results)
        print(f"[END] Product Agent")
        print("-----" * 20)
        return state

    def search_with_escalating_filters(
        self, enhanced_query, metadata_filter, vector_store, embed_query_fn
    ):
        """
        Performs a similarity search with escalating metadata filters.

        Args:
            enhanced_query (str): The processed user query.
            metadata_filter (List[Dict]): Extracted metadata filters.
            vector_store: The vector store instance with similarity search methods.
            embed_query_fn (Callable): Function to embed the query into a vector.

        Returns:
            List: Search results from the vector store.
        """

        def build_filter(filters_subset):
            filter_conditions = []
            for filter_obj in filters_subset:
                for key, value in filter_obj.items():
                    if isinstance(value, list):
                        for v in value:
                            filter_conditions.append(
                                FieldCondition(
                                    key=f"metadata.{key}",
                                    match=MatchValue(value=v),
                                )
                            )
                    else:
                        filter_conditions.append(
                            FieldCondition(
                                key=f"metadata.{key}",
                                match=MatchValue(value=value),
                            )
                        )
            return Filter(must=filter_conditions) if filter_conditions else None

        print(f"[DEBUG] Metadata filter: {metadata_filter}")

        search_results = []

        if metadata_filter:
            # Define filter priority (high to low)
            filter_priority = ["type", "sku", "id", "category", "name"]

            # Merge all metadata filter dicts into one
            combined_filter = {}
            for item in metadata_filter:
                combined_filter.update(item)

            # Try filtering by progressively removing lowest priority filters
            for i in range(len(filter_priority)):
                # Build subset by keeping only top-N priority filters
                filtered_subset = [
                    {k: combined_filter[k]}
                    for k in filter_priority[: len(filter_priority) - i]
                    if k in combined_filter
                ]
                if not filtered_subset:
                    continue

                query_filter = build_filter(filtered_subset)
                print(f"[DEBUG] Trying with filters: {filtered_subset}")

                filtered_results = vector_store.similarity_search(
                    query=enhanced_query,
                    k=20,
                    filter=query_filter,
                )

                if filtered_results:
                    print(
                        f"[DEBUG] Found {len(filtered_results)} results with filters: {filtered_subset}"
                    )
                    return filtered_results  # Early return on success
                else:
                    print(f"[DEBUG] No results with filters: {filtered_subset}")

        # Fallback: no filters
        print(
            "[DEBUG] No results from metadata filtering, performing vector search without filters"
        )
        return vector_store.similarity_search_by_vector(
            embedding=embed_query_fn(enhanced_query),
            k=18,
        )


class ProductMetadataFilterExtractor:

    def __init__(self, llm_config: LLMConfig):
        self.llm = llm_config.get_llm()

    def extract_product_metadata_filters(self, user_query: str):
        """
        Uses an LLM to extract metadata filters for product documents based on the user query.
        The filters will be used to identify document types such as 'specifications', 'pricing', etc.

        Returns a list of metadata filters, each being a dict with keys like:
        - "sku"
        - "type"
        """

        few_shot_examples = """
        Examples:

        Query: "Show me the description of product with SKU 10232"
        Output: [{"sku": "10232", "type": "description"}]

        Query: "Give me pricing details of the product named 'Nike Air Zoom Pegasus 38'"
        Output: [{"name": "Nike Air Zoom Pegasus 38", "type": "pricing"}]

        Query: "What categories does product ID 789 belong to?"
        Output: [{"id": 789, "type": "custom_attributes"}]

        Query: "Tell me the visibility and status of 'Adidas Ultraboost 22'"
        Output: [{"name": "Adidas Ultraboost 22", "type": "visibility"}, {"name": "Adidas Ultraboost 22", "type": "status"}]

        Query: "How many orders have been placed for product with SKU 44567?"
        Output: [{"sku": "44567", "type": "orders_details"}]

        Query: "Summarize the product called 'Apple iPhone 14 Pro'"
        Output: [{"name": "Apple iPhone 14 Pro", "type": "summary"}]

        Query: "What is the price and type ID of product 1122?"
        Output: [{"sku": "1122", "type": "pricing"}, {"sku": "1122", "type": "type_id"}]

        Query: "Find all images and custom attributes for the product 'Samsung Galaxy S24 Ultra'"
        Output: [{"name": "Samsung Galaxy S24 Ultra", "type": "image"}, {"name": "Samsung Galaxy S24 Ultra", "type": "custom_attributes"}]

        Query: "How much stock is there for the product 'Sony WH-1000XM4'"
        Output: [{"name": "Sony WH-1000XM4", "type": "stock_info"}]

        Query: "List the supplier and brand for product with ID 321"
        Output: [{"id": 321, "type": "supplier"}, {"id": 321, "type": "brand"}]

        Query: "what is price of TerrasSchwank 4 Aardgas ?"
        Output: [{"name": "TerrasSchwank 4 Aardgas", "type": "pricing"}
        """

        system_prompt = f"""
        You are an intelligent assistant responsible for extracting structured metadata from user queries related to product data.
        Based on the user query, return a list of metadata filters in **valid JSON format**.

        Use only the following `type` values:
        - "description"
        - "pricing"
        - "category"
        - "brand"
        - "supplier"
        - "image"
        - "summary"
        - "custom_attributes"
        - "extension_attributes"
        - "stock_info"
        - "orders_details"

        Rules:
        - Assign any attribute-related field to "custom_attributes".
        - Extract `sku` from the query if present.
        - If multiple SKUs are mentioned, return multiple filter objects.
        - If no SKU is found, return nothing.
        - For stock-related and backorders queries, use "stock_info" as the type.
        - Return **only** valid JSON — no extra text or explanation.
        - [STRICTLY] Only include filters that are explicitly mentioned in the query—do not make assumptions.
        - If a field cannot be clearly classified, assign it to "custom_attributes".
        - If "Context from other agents" is present than focus more on name of item rather than SKU.

        {few_shot_examples}
        """

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f'Query: "{user_query}"\nOutput:'),
        ]

        try:
            response = self.llm.invoke(messages).content.strip()
            print("metadata filter")
            print(type(response), "\n", response, "\n")

            if not isinstance(response, list):
                if "metadata_filters" in response:
                    filters = json.loads(response["metadata_filters"])
                if isinstance(response, dict):
                    filters = [json.loads(response)]
                else:
                    filters = json.loads(response)

            print("metadata filter AFTER REFINATION")
            print(type(filters), "\n", filters, "\n")
            if not isinstance(filters, list):
                logging.warning(f"Unexpected format: {filters}")
                return None

            valid_types = {
                "description",
                "pricing",
                "category",
                "brand",
                "supplier",
                "image",
                "summary",
                "custom_attributes",
                "extension_attributes",
                "stock_info",
                "orders_details",
            }

            return [
                f
                for f in filters
                if isinstance(f, dict) and "type" in f and f["type"] in valid_types
            ]

        except Exception as e:
            logging.error(f"JSON decoding failed: {e} | Response: {response}")
            return None
