import json
import uuid
from typing import Any, Dict
from langchain_core.documents import Document
from utils.embed_utils import embed_and_upsert_products


def flatten_dict(
    d: Dict[str, Any], parent_key: str = "", sep: str = "."
) -> Dict[str, Any]:
    """
    Flattens a nested dictionary into a single level using dot notation.
    """
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def preprocess_product_data(**context):
    ti = context["ti"]
    data = ti.xcom_pull(task_ids="fetch_products")
    product_orders_data = ti.xcom_pull(
        task_ids="fetch_product_orders", key="product_orders"
    )

    products = data.get("products", [])
    if not products:
        print("[ERROR] No products found in XCom pull.")
        return

    all_docs = []
    all_doc_log = {}
    # products = products[:10]

    print(f"Products ---> \n{products[0]}\n type of ---> {type(products)}")
    for product in products:
        sku = product.get("sku")
        print("+++++++++++++++++++  SKU  ++++++++++++++++++++ :", sku)
        doc_log: dict[str, list[str]] = {}
        single_product_docs = []

        metadata_base = {
            "sku": sku,
            "id": product.get("id"),
            "name": product.get("name"),
            "type_id": product.get("type_id"),
            "status": product.get("status"),
            "visibility": product.get("visibility"),
            "price": product.get("price"),
        }

        # Custom Attributes
        custom_attributes = product.get("custom_attributes", [])
        if custom_attributes:
            doc_uuid = str(uuid.uuid4())
            doc = Document(
                page_content=f"For Product {product.get('name')}, sku --> {product.get('sku')} custom_attributes are {json.dumps(custom_attributes, indent=2)}",
                metadata={
                    **metadata_base,
                    "type": "custom_attributes",
                    "doc_id": doc_uuid,
                },
            )
            single_product_docs.append(doc)
            doc_log["custom_attributes"] = [doc_uuid]

        # Order Details
        if product_orders_data and sku in product_orders_data:
            orders = product_orders_data[sku]
            if orders:
                # Extract key information from orders
                order_summary = []
                order_ids = set()

                for order in orders:
                    order_id = order.get("order_id")
                    order_ids.add(order_id)

                    order_summary.append(
                        {
                            "order_id": order_id,
                            "item_id": order.get("item_id"),
                            "name": order.get("name"),
                            "created_at": order.get("created_at"),
                            "qty_ordered": order.get("qty_ordered"),
                            "price": order.get("price"),
                            "price_incl_tax": order.get("price_incl_tax"),
                            "row_total": order.get("row_total"),
                            "row_total_incl_tax": order.get("row_total_incl_tax"),
                            "tax_percent": order.get("tax_percent"),
                            "status": {
                                "qty_invoiced": order.get("qty_invoiced"),
                                "qty_shipped": order.get("qty_shipped"),
                                "qty_refunded": order.get("qty_refunded"),
                                "qty_canceled": order.get("qty_canceled"),
                            },
                        }
                    )

                doc_uuid = str(uuid.uuid4())
                doc = Document(
                    page_content=f"For Product {product.get('name')}, sku --> {sku} order details: Orders count: {len(orders)}, Order IDs: {list(order_ids)}, Order details: {json.dumps(order_summary, indent=2)}",
                    metadata={
                        **metadata_base,
                        "type": "orders_details",
                        "doc_id": doc_uuid,
                        "orders_count": len(orders),
                        "order_ids": list(order_ids),
                    },
                )
                single_product_docs.append(doc)
                doc_log["orders_details"] = [doc_uuid]

        # Extension Attributes
        extension_attributes = product.get("extension_attributes", {})
        if extension_attributes:
            doc_uuid = str(uuid.uuid4())
            doc = Document(
                page_content=f"For Product {product.get('name')}, sku --> {product.get('sku')} extension_attributes are {json.dumps(extension_attributes, indent=2)}",
                metadata={
                    **metadata_base,
                    "type": "extension_attributes",
                    "doc_id": doc_uuid,
                },
            )
            single_product_docs.append(doc)
            doc_log["extension_attributes"] = [doc_uuid]

        # Description
        description = next(
            (
                attr["value"]
                for attr in custom_attributes
                if attr["attribute_code"] == "description"
            ),
            None,
        )
        if description:
            doc_uuid = str(uuid.uuid4())
            doc = Document(
                page_content=f"For Product {product.get('name')}, sku --> {product.get('sku')} description is {description}",
                metadata={**metadata_base, "type": "description", "doc_id": doc_uuid},
            )
            single_product_docs.append(doc)
            doc_log["description"] = [doc_uuid]

        #  Pricing
        price = product.get("price")
        if price is not None:
            doc_uuid = str(uuid.uuid4())
            doc = Document(
                page_content=f"For Product {product.get('name')}, sku --> {product.get('sku')} pricing is {price}",
                metadata={**metadata_base, "type": "pricing", "doc_id": doc_uuid},
            )
            single_product_docs.append(doc)
            doc_log["pricing"] = [doc_uuid]

        #  Category
        categories = product.get("category_ids", [])
        if categories:
            doc_uuid = str(uuid.uuid4())
            doc = Document(
                page_content=f"For Product {product.get('name')}, sku --> {product.get('sku')} category are {categories}",
                metadata={**metadata_base, "type": "category", "doc_id": doc_uuid},
            )
            single_product_docs.append(doc)
            doc_log["category"] = [doc_uuid]

        # Brand
        brand = next(
            (
                attr["value"]
                for attr in custom_attributes
                if attr["attribute_code"] == "brand"
            ),
            None,
        )
        if brand:
            doc_uuid = str(uuid.uuid4())
            doc = Document(
                page_content=f"Product {product.get('name')} belongs to Brand: {brand}",
                metadata={**metadata_base, "type": "brand", "doc_id": doc_uuid},
            )
            single_product_docs.append(doc)
            doc_log["brand"] = [doc_uuid]

        ignored_keys = ["custom_attributes", "extension_attributes"]
        base_product_info = {k: v for k, v in product.items() if k not in ignored_keys}
        flattened = flatten_dict(base_product_info)

        doc_uuid = str(uuid.uuid4())
        summary_doc = Document(
            page_content=f" For Product {product.get('name')}, sku --> {product.get('sku')} summary is {json.dumps(flattened, indent=2)}",
            metadata={**metadata_base, "type": "summary", "doc_id": doc_uuid},
        )
        single_product_docs.append(summary_doc)
        doc_log["summary"] = [doc_uuid]

        print(
            f"[PREPROCESS] Processed product {sku} with {len(single_product_docs)} documents."
        )
        all_doc_log[sku] = doc_log
        all_docs.extend(single_product_docs)

    print(
        f"[QDRANT] Final upsert of {len(all_docs)} total documents across {len(products)} products."
    )
    embed_and_upsert_products(all_docs, all_doc_log, is_stock_info=False)

    return True
