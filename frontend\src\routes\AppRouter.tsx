import { Routes as RouterRoutes, Route } from 'react-router-dom';
import { routes } from '@/config/routesConfig';

function AppRouter() {
  return (
    <RouterRoutes>
      {routes.map(({ path, element, children }, parentIndex) => (
        <Route key={parentIndex} path={path} element={element}>
          {children?.map(({ path: childPath, element: childElement, primary }, childIndex) => (
            <Route
              key={`${parentIndex}-${childIndex}`}
              path={primary ? undefined : childPath}
              index={primary || childPath === '' || childPath === undefined}
              element={childElement}
            />
          ))}
        </Route>
      ))}
    </RouterRoutes>
  );
}

export default AppRouter;
