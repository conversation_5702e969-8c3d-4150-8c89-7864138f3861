{"userManagement": {"title": "User Management", "role": {"label": "Role", "types": {"supervisor": "supervisor", "admin": "admin"}}, "noUsersFound": "No users found", "rowsPerPage": "Rows per page"}, "actions": {"addNewUser": "Add New User", "editUser": "Edit User", "deleteUser": "Delete User", "createUser": "Create a User", "deleteAll": "Delete"}, "userRole": "User Role", "deleteConfirmation": {"message": "Are you sure you want to delete this user? This action cannot be undone.", "title": "Confirm Delete User"}, "deleteAllConfirmation": {"message": "Are you sure you want to delete selected users? This action cannot be undone.", "title": "Confirm Delete Selected Users"}, "selectRole": "Select Role", "errors": {"gettingUsers": "Error While Getting Users!", "deletingUser": "Error While Deleting User!", "creatingUser": "Create User Error!", "userAlreadyExists": "User already exists!", "userNotFound": "User not found!", "emailRegistered": "Email already registered!"}, "success": {"userDeleted": "User Deleted Successfully!", "userUpdated": "User Updated Successfully!", "userAdded": "User Added Successfully!"}}