import os
import requests
from typing import Any, Dict, List
from datetime import datetime
from utils.embed_utils import BaseMagentoClient


class CustomerClient(BaseMagentoClient):
    """Client for interacting with Magento API for customer data."""

    def fetch_customers(
        self, page_size: int = 10, current_page: int = 1, max_items: int = 10
    ) -> List[Dict[str, Any]]:
        """Fetch customers from Magento API."""
        endpoint = f"{self.base_url}/rest/all/V1/customers/search"
        customers = []

        while True:
            params = {
                "searchCriteria[pageSize]": page_size,
                "searchCriteria[currentPage]": current_page,
            }

            response = requests.get(endpoint, headers=self.get_headers(), params=params)
            response.raise_for_status()

            data = response.json()
            items = data.get("items", [])
            customers.extend(items)

            if len(customers) >= max_items or len(items) == 0:
                break
            current_page += 1

        return customers[:max_items]

    def fetch_customer_orders(self, customer_email: str) -> List[Dict[str, Any]]:
        """Fetch orders for a specific customer by email."""
        endpoint = f"{self.base_url}/rest/all/V1/orders"

        params = {
            "searchCriteria[filterGroups][0][filters][0][field]": "customer_email",
            "searchCriteria[filterGroups][0][filters][0][value]": customer_email,
            "searchCriteria[filterGroups][0][filters][0][conditionType]": "eq",
        }

        response = requests.get(endpoint, headers=self.get_headers(), params=params)
        response.raise_for_status()

        return response.json().get("items", [])

    def fetch_customer_cart(self, customer_email: str) -> List[Dict[str, Any]]:
        """Fetch cart information for a specific customer by email."""
        endpoint = f"{self.base_url}/rest/all/V1/carts/search"

        params = {
            "searchCriteria[filterGroups][0][filters][0][field]": "customer_email",
            "searchCriteria[filterGroups][0][filters][0][value]": customer_email,
            "searchCriteria[filterGroups][0][filters][0][conditionType]": "eq",
        }

        response = requests.get(endpoint, headers=self.get_headers(), params=params)
        response.raise_for_status()

        return response.json().get("items", [])

    def fetch_customer_invoices(self, order_id: str) -> List[Dict[str, Any]]:
        """Fetch invoices for a specific order."""
        endpoint = f"{self.base_url}/rest/all/V1/invoices"

        params = {
            "searchCriteria[filterGroups][0][filters][0][field]": "order_id",
            "searchCriteria[filterGroups][0][filters][0][value]": order_id,
            "searchCriteria[filterGroups][0][filters][0][conditionType]": "eq",
        }

        response = requests.get(endpoint, headers=self.get_headers(), params=params)
        response.raise_for_status()

        return response.json().get("items", [])


def fetch_magento_customers(**context):
    """Airflow task to fetch Magento customers."""
    client = CustomerClient()

    try:
        # Fetch customers (limit to 10 for testing)
        customers = client.fetch_customers(page_size=10, current_page=1, max_items=10)

        print(f"[FETCH] Fetched {len(customers)} customers from Magento.")

        # Store the results for the next task
        context["task_instance"].xcom_push(key="customers", value={"items": customers})

        return {"customers": customers}

    except Exception as e:
        print(f"Error fetching customers from Magento: {str(e)}")
        raise
