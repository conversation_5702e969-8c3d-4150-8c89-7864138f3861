import React from 'react';

import { Box, TextField, IconButton, Fab } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import { useTranslations } from '@/i18n/hooks/useTranslations';
import { useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/system';
import { ArrowDownward } from '@mui/icons-material';

type Props = {
  message: string;
  setMessage: (message: string) => void;
  handleSendMessage: () => void;
  handleKeyPress: (e: React.KeyboardEvent) => void;
  isCurrentSessionLoading: boolean;
  sessionLoading: boolean;
  handleScrollToBottomClick: () => void;
  showScrollToBottom: boolean;
};

function ChatInput({
  message,
  setMessage,
  handleSendMessage,
  handleKeyPress,
  isCurrentSessionLoading,
  sessionLoading,
  handleScrollToBottomClick,
  showScrollToBottom,
}: Props) {
  const { t: tChat } = useTranslations('chat');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <>
      {showScrollToBottom && (
        <Fab
          size={isMobile ? 'medium' : 'small'}
          color="primary"
          aria-label="scroll to bottom"
          onClick={handleScrollToBottomClick}
          sx={{
            position: 'absolute',
            bottom: isMobile ? 100 : 120,
            right: isMobile ? 16 : 24,
            zIndex: 1000,
            bgcolor: '#1976d2',
            color: 'white',
            opacity: showScrollToBottom ? 1 : 0,
          }}
        >
          <ArrowDownward
            sx={{
              fontSize: isMobile ? 24 : 20,
              transition: 'transform 0.2s ease',
            }}
          />
        </Fab>
      )}
      <Box
        component="form"
        sx={{
          p: 2,
          bgcolor: '#f5f7fa',
          display: 'flex',
          alignItems: 'center',
          pt: 0,
        }}
        onSubmit={(e) => {
          e.preventDefault();
          handleSendMessage();
        }}
      >
        <TextField
          autoFocus
          fullWidth
          placeholder={tChat('conversation.typeMessage')}
          variant="outlined"
          size="medium"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyPress}
          disabled={isCurrentSessionLoading || sessionLoading}
          sx={{
            mr: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: '24px',
              bgcolor: 'white',
              transition: 'box-shadow 0.2s ease',
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none',
            },
          }}
          slotProps={{
            input: {
              sx: { py: 0.5, px: 2 },
            },
          }}
        />
        <IconButton
          aria-label="send message"
          onClick={handleSendMessage}
          disabled={isCurrentSessionLoading || sessionLoading || !message.trim()}
          sx={{
            bgcolor: message.trim() ? '#0d47a1' : '#c9c9c9',
            color: 'white',
            '&:hover': {
              bgcolor: message.trim() ? '#1565c0' : '#c9c9c9',
            },
            width: 48,
            height: 48,
          }}
        >
          <SendIcon />
        </IconButton>
      </Box>
    </>
  );
}

export default ChatInput;
