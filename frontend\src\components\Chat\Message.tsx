import { Box, Paper } from '@mui/material';
import ReactMarkdown from 'react-markdown';
import { MessagesType } from '@/types/types';
import { memo } from 'react';
import MarkdownComponents from '@/components/Chat/MarkdownComponent';

type Props = {
  msg: MessagesType;
  isMobile: boolean;
};

function Message({ msg, isMobile }: Props) {
  return (
    <>
      {/* User Message */}
      {msg.message && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            mb: 2,
            maxWidth: '100%',
          }}
        >
          <Paper
            elevation={2}
            sx={{
              p: 2,
              maxWidth: isMobile ? '90%' : '70%',
              bgcolor: '#FFF',
              color: 'inherit',
              borderRadius: '10px 0px 10px 10px',
              boxShadow: '0px 2px 4px rgba(0,0,0,0.05)',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                boxShadow: '0px 4px 8px rgba(0,0,0,0.1)',
              },
            }}
          >
            <ReactMarkdown components={MarkdownComponents(true)}>{msg.message}</ReactMarkdown>
          </Paper>
        </Box>
      )}

      {/* Bot Response */}
      {msg.response && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-start',
            mb: 2,
            maxWidth: '100%',
          }}
        >
          <Paper
            elevation={2}
            sx={{
              p: 2,
              maxWidth: isMobile ? '90%' : '70%',
              bgcolor: '#5b7bc0',
              color: 'white',
              borderRadius: '0px 10px 10px 10px',
              boxShadow: '0px 2px 4px rgba(0,0,0,0.05)',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                boxShadow: '0px 4px 8px rgba(0,0,0,0.1)',
              },
            }}
          >
            <ReactMarkdown components={MarkdownComponents(false)}>{msg.response}</ReactMarkdown>
          </Paper>
        </Box>
      )}
    </>
  );
}

export default memo(Message);
