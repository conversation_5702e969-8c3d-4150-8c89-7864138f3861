import requests
from datetime import datetime
from dotenv import load_dotenv
from typing import Any, Dict, List
from utils.embed_utils import BaseMagentoClient

load_dotenv()


class MagentoClient(BaseMagentoClient):
    """Client for interacting with Magento API for product data."""

    def fetch_products(
        self,
        since: datetime,
        current_page: int = 1,
        page_size: int = 100,
        max_items: int = 100,
    ) -> List[Dict[str, Any]]:
        """Fetch products updated since a given date, up to max_items."""

        endpoint = f"{self.base_url}/rest/V1/products"
        products = []

        while True:
            params = {
                "searchCriteria[filterGroups][0][filters][0][field]": "created_at",
                "searchCriteria[filterGroups][0][filters][0][value]": since.isoformat(),
                "searchCriteria[filterGroups][0][filters][0][condition_type]": "gteq",
                "searchCriteria[currentPage]": current_page,
                "searchCriteria[pageSize]": page_size,
            }

            response = requests.get(endpoint, headers=self.get_headers(), params=params)
            response.raise_for_status()

            data = response.json()
            items = data.get("items", [])
            products.extend(items)

            if len(products) >= max_items or len(items) == 0:
                break
            current_page += 1

        return products[:max_items]

    def fetch_product_by_sku(self, sku: str) -> Dict[str, Any]:
        """Fetch a single product by SKU."""

        endpoint = f"{self.base_url}/rest/V1/products/{sku}"
        response = requests.get(endpoint, headers=self.get_headers())
        response.raise_for_status()
        return response.json()
        
    def fetch_product_orders(self, sku: str) -> List[Dict[str, Any]]:
        """Fetch orders for a specific product by SKU."""
        
        endpoint = f"{self.base_url}/rest/V1/orders/items"
        params = {
            "searchCriteria[filterGroups][0][filters][0][field]": "sku",
            "searchCriteria[filterGroups][0][filters][0][value]": sku,
            "searchCriteria[filterGroups][0][filters][0][condition_type]": "eq"
        }
        
        response = requests.get(endpoint, headers=self.get_headers(), params=params)
        response.raise_for_status()
        
        return response.json().get("items", [])


def fetch_magento_products(**context):
    """Airflow task to fetch Magento products for testing."""

    skus = context["params"].get("skus", [])

    client = MagentoClient()

    # Default to a very early date to fetch all products
    since = datetime(2000, 1, 1)

    try:
        if skus:
            products = []
            for sku in skus:
                product = client.fetch_product_by_sku(sku)
                if product:
                    products.append(product)
            print(f"[FETCH] Fetched {len(products)} specific products by SKU.")
        else:
            # to constraint product fetch process to first 10 products only.
            # [To remove the constraint remove/increase the max_items, page_size values]
            products = client.fetch_products(since=since, max_items=10, page_size=10)

            print(f"[FETCH] Fetched {len(products)} products from Magento.")
            # Store the results for the next task
            context["task_instance"].xcom_push(
                key="products", value={"items": products}
            )

        return {"products": products}

    except Exception as e:
        print(f"Error fetching products from Magento: {str(e)}")
        raise


def fetch_product_orders(**context):
    """Airflow task to fetch orders for products."""
    
    ti = context["ti"]
    data = ti.xcom_pull(task_ids="fetch_products")
    
    products = data.get("products", [])
    if not products:
        print("[ERROR] No products found in XCom pull.")
        return
    
    client = MagentoClient()
    product_orders = {}
    
    try:
        for product in products:
            sku = product.get("sku")
            if not sku:
                continue
                
            print(f"[FETCH] Fetching orders for product SKU: {sku}")
            orders = client.fetch_product_orders(sku)
            
            if orders:
                product_orders[sku] = orders
                print(f"[FETCH] Found {len(orders)} orders for product SKU: {sku}")
            else:
                print(f"[FETCH] No orders found for product SKU: {sku}")
        
        # Store the results for the next task
        context["task_instance"].xcom_push(
            key="product_orders", value=product_orders
        )
        
        return {"product_orders": product_orders}
        
    except Exception as e:
        print(f"Error fetching product orders from Magento: {str(e)}")
        raise
