import json
import uuid
import requests
from typing import Any, Dict, List
from langchain_core.documents import Document
from utils.embed_utils import embed_and_upsert_products, BaseMagentoClient


class MagentoClient(BaseMagentoClient):
    """Client for interacting with Magento API for stock data."""

    def fetch_stock_info(
        self,
        sku: str,
    ) -> List[Dict[str, Any]]:
        """Fetch stock information for a list of SKUs."""
        endpoint = f"{self.base_url}/rest/V1/stockItems/{sku}"
        response = requests.get(endpoint, headers=self.get_headers())
        response.raise_for_status()

        return response.json()


def stock_info_data_fetch(**context):
    """Fetch stock information for products and store in QdrantDB."""
    ti = context["ti"]
    data = ti.xcom_pull(task_ids="fetch_products")

    products = data.get("products", [])
    if not products:
        print("[ERROR] No products found in XCom pull.")
        return

    all_docs = []
    all_doc_log = {}

    for product in products:
        sku = product.get("sku")
        print("+++++++++++++++++++  SKU  ++++++++++++++++++++ :", sku)

        client = MagentoClient()
        stock_info = client.fetch_stock_info(sku)
        print("Stock Info --> ", stock_info)

        if not stock_info:
            print(f"[WARNING] No stock information found for SKU: {sku}")
            continue

        doc_log: dict[str, list[str]] = {}

        # Create base metadata from product information
        metadata_base = {
            "sku": sku,
            "id": product.get("id"),
            "name": product.get("name"),
            "type_id": product.get("type_id"),
            "status": product.get("status"),
        }

        # Create document for stock information
        doc_uuid = str(uuid.uuid4())
        doc = Document(
            page_content=f"For Product {product.get('name')}, sku --> {sku} stock information is {json.dumps(stock_info, indent=2)}",
            metadata={
                **metadata_base,
                "type": "stock_info",
                "doc_id": doc_uuid,
                "qty": stock_info.get("qty", 0),
                "is_in_stock": stock_info.get("is_in_stock", False),
            },
        )

        all_docs.append(doc)
        doc_log["stock_info"] = [doc_uuid]
        all_doc_log[sku] = doc_log

        print(f"[PREPROCESS] Processed stock info for product {sku}")

    if all_docs:
        print(
            f"[QDRANT] Final upsert of {len(all_docs)} stock info documents across {len(all_doc_log)} products."
        )
        embed_and_upsert_products(all_docs, all_doc_log, is_stock_info=True)
    else:
        print("[WARNING] No stock information documents to upsert.")

    return True
