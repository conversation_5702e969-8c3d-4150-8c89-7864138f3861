Magento-bot Architecture Diagram
============================

+-------------------------------------------------------------------------------------------------------------+
|                                           MAGENTO-BOT SYSTEM                                                 |
+-------------------------------------------------------------------------------------------------------------+
|                                                                                                             |
|  +-------------------+    +-------------------+    +-------------------+    +-------------------+           |
|  |                   |    |                   |    |                   |    |                   |           |
|  |  Magento API      |    |  PDF Documents    |    |  CRM System       |    |  Other Sources    |           |
|  |                   |    |                   |    |                   |    |                   |           |
|  +--------+----------+    +--------+----------+    +--------+----------+    +--------+----------+           |
|           |                        |                        |                        |                      |
|           v                        v                        v                        v                      |
|  +-----------------------------------------------------------------------------------------------------------+
|  |                                        DATA PIPELINE (Airflow)                                           |
|  +-----------------------------------------------------------------------------------------------------------+
|  |                                                                                                          |
|  |  +-------------------+    +-------------------+    +-------------------+    +-------------------+        |
|  |  |                   |    |                   |    |                   |    |                   |        |
|  |  |  Order Data       |    |  PDF Data         |    |  Product Data     |    |  CRM Data         |        |
|  |  |  Processing       |    |  Processing       |    |  Processing       |    |  Processing       |        |
|  |  |                   |    |                   |    |                   |    |                   |        |
|  |  +--------+----------+    +--------+----------+    +--------+----------+    +--------+----------+        |
|  |           |                        |                        |                        |                   |
|  |           v                        v                        v                        v                   |
|  |  +-----------------------------------------------------------------------------------------------------------+
|  |  |                                      VECTOR DATABASES (Qdrant)                                         |
|  |  +-----------------------------------------------------------------------------------------------------------+
|  |  |                                                                                                        |
|  |  |  +-------------------+    +-------------------+    +-------------------+    +-------------------+      |
|  |  |  |                   |    |                   |    |                   |    |                   |      |
|  |  |  |  Order Vector DB  |    |  PDF Vector DB    |    |  Product Vector DB|    |  CRM Vector DB    |      |
|  |  |  |                   |    |                   |    |                   |    |                   |      |
|  |  |  +-------------------+    +-------------------+    +-------------------+    +-------------------+      |
|  |  |                                                                                                        |
|  |  +-----------------------------------------------------------------------------------------------------------+
|  |                                                                                                          |
|  +-----------------------------------------------------------------------------------------------------------+
|                                                     |                                                        |
|                                                     v                                                        |
|  +-----------------------------------------------------------------------------------------------------------+
|  |                                       AGENTIC AI SYSTEM                                                  |
|  +-----------------------------------------------------------------------------------------------------------+
|  |                                                                                                          |
|  |  +-------------------+                                                                                   |
|  |  |                   |                                                                                   |
|  |  |  Master Agent     |                                                                                   |
|  |  |                   |                                                                                   |
|  |  +--------+----------+                                                                                   |
|  |           |                                                                                              |
|  |           v                                                                                              |
|  |  +-----------------------------------------------------------------------------------------------------------+
|  |  |                                                                                                        |
|  |  |  +-------------------+    +-------------------+    +-------------------+    +-------------------+      |
|  |  |  |                   |    |                   |    |                   |    |                   |      |
|  |  |  |  Order Agent      |    |  PDF Agent        |    |  Product Agent    |    |  CRM Agent        |      |
|  |  |  |                   |    |                   |    |                   |    |                   |      |
|  |  |  +-------------------+    +-------------------+    +-------------------+    +-------------------+      |
|  |  |                                                                                                        |
|  |  |  +-------------------+    +-------------------+                                                        |
|  |  |  |                   |    |                   |                                                        |
|  |  |  |  Inventory Agent  |    |  Combiner Agent   |                                                        |
|  |  |  |                   |    |                   |                                                        |
|  |  |  +-------------------+    +-------------------+                                                        |
|  |  |                                                                                                        |
|  |  +-----------------------------------------------------------------------------------------------------------+
|  |                                                                                                          |
|  +-----------------------------------------------------------------------------------------------------------+
|                                                     |                                                        |
|                                                     v                                                        |
|  +-----------------------------------------------------------------------------------------------------------+
|  |                                         API LAYER (FastAPI)                                              |
|  +-----------------------------------------------------------------------------------------------------------+
|  |                                                                                                          |
|  |  +-------------------+    +-------------------+    +-------------------+    +-------------------+        |
|  |  |                   |    |                   |    |                   |    |                   |        |
|  |  |  Auth Routes      |    |  Chat Routes      |    |  User Routes      |    |  Session Routes   |        |
|  |  |                   |    |                   |    |                   |    |                   |        |
|  |  +-------------------+    +-------------------+    +-------------------+    +-------------------+        |
|  |                                                                                                          |
|  +-----------------------------------------------------------------------------------------------------------+
|                                                     |                                                        |
|                                                     v                                                        |
|  +-----------------------------------------------------------------------------------------------------------+
|  |                                       DATABASE LAYER                                                     |
|  +-----------------------------------------------------------------------------------------------------------+
|  |                                                                                                          |
|  |  +-------------------+    +-------------------+                                                          |
|  |  |                   |    |                   |                                                          |
|  |  |  MongoDB          |    |  PostgreSQL       |                                                          |
|  |  |  (Session/User)   |    |  (Airflow)        |                                                          |
|  |  |                   |    |                   |                                                          |
|  |  +-------------------+    +-------------------+                                                          |
|  |                                                                                                          |
|  +-----------------------------------------------------------------------------------------------------------+
|                                                                                                             |
+-------------------------------------------------------------------------------------------------------------+

Directory Structure:
===================

magento-bot/
├── agentic_ai/          # AI Agents and orchestration
│   ├── agents/          # Individual specialized agents
│   ├── orchestrator/    # Agent interaction graph
│   ├── state/           # State management
│   └── utils/           # AI utilities
│
├── api/                 # FastAPI application
│   ├── database/        # Database models
│   ├── routes/          # API endpoints
│   ├── schemas/         # Data schemas
│   ├── main.py          # API entry point
│   └── utils.py         # API utilities
│
├── data_pipeline/       # Airflow DAGs and scripts
│   ├── dags/            # Airflow DAG definitions
│   ├── database/        # Database connections
│   ├── scripts/         # Data processing scripts
│   │   ├── order_data/  # Order processing
│   │   ├── pdf_data/    # PDF processing
│   │   └── product_data/# Product processing
│   └── utils/           # Pipeline utilities
│
├── configs/             # Configuration files
│   └── airflow_config.yaml
│
├── docker/              # Docker-related files
│   ├── Dockerfile.api
│   ├── Dockerfile.airflow
│   └── entrypoint.sh
│
├── tests/               # Test suite
│
├── docker-compose.yml   # Docker services
├── Makefile             # Build commands
├── requirements.txt     # Dependencies
└── README.md            # Documentation
