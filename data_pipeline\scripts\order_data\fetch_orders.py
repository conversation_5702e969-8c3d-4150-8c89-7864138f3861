import os
import requests
from dotenv import load_dotenv
from typing import Any, Dict, List
from datetime import datetime, timedelta
from utils.embed_utils import BaseMagentoClient

load_dotenv()


class MagentoClient(BaseMagentoClient):
    """Client for interacting with Magento API for order data."""

    def fetch_orders(
        self, start_date: datetime, end_date: datetime
    ) -> List[Dict[str, Any]]:
        """Fetch orders from Magento within the specified date range."""
        endpoint = f"{self.base_url}/rest/V1/orders"

        params = {
            # "searchCriteria[filterGroups][0][filters][0][field]": "created_at",
            # "searchCriteria[filterGroups][0][filters][0][value]": start_date.isoformat(),
            # "searchCriteria[filterGroups][0][filters][0][condition_type]": "gteq",
            # "searchCriteria[filterGroups][1][filters][0][field]": "created_at",
            # "searchCriteria[filterGroups][1][filters][0][value]": end_date.isoformat(),
            # "searchCriteria[filterGroups][1][filters][0][condition_type]": "lteq",
            "searchCriteria[pageSize]": 100000,
            "searchCriteria[currentPage]": 1,
        }

        response = requests.get(endpoint, headers=self.get_headers(), params=params)
        response.raise_for_status()

        return response.json().get("items", [])

    def fetch_order_by_id(self, order_id: int) -> Dict[str, Any]:
        """Fetch a single order by its ID."""

        endpoint = f"{self.base_url}/rest/V1/orders/{order_id}"
        response = requests.get(endpoint, headers=self.get_headers())
        response.raise_for_status()
        return response.json()


def fetch_magento_orders(**context):
    """Airflow task to fetch Magento orders."""

    client = MagentoClient()

    # Get orders for the last hour
    end_date = datetime.now()
    start_date = end_date - timedelta(days=100)
    order_ids = context["params"].get("order_ids", [])

    try:
        if order_ids:
            # Fetch specific orders by ID
            orders = []
            for order_id in order_ids:
                order = client.fetch_order_by_id(order_id)
                if order:
                    orders.append(order)
            print(f"[FETCH] Fetched {len(orders)} specific orders.")
        else:
            # Fallback: Fetch orders in a date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=100)
            orders = client.fetch_orders(start_date, end_date)
            print(f"[FETCH] Fetched {len(orders)} orders from date range.")

        context["task_instance"].xcom_push(key="orders", value=orders)
        return {"orders": orders}

    except Exception as e:
        print(f"Error fetching data from Magento: {str(e)}")
        raise
