{"name": "tess", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/styled-engine-sc": "^7.0.2", "axios": "^1.8.4", "i18next": "^25.1.1", "i18next-browser-languagedetector": "^8.1.0", "jwt-decode": "^4.0.0", "lodash.debounce": "^4.0.8", "lottie-react": "^2.4.1", "notistack": "^3.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.5.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.0", "styled-components": "^6.1.17", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/lodash.debounce": "^4.0.9", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}